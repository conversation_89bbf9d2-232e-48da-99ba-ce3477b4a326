import { StatusBar } from 'expo-status-bar';
import { StyleSheet } from 'react-native';
import { PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { DynamicThemeProvider, useDynamicTheme } from './src/contexts/DynamicThemeProvider';
import AppNavigator from './src/navigation/AppNavigator';

// App content that uses the dynamic theme
function AppContent() {
  const theme = useDynamicTheme();

  return (
    <SafeAreaProvider>
      <PaperProvider theme={theme}>
        <AppNavigator />
        <StatusBar style={theme.isDark ? 'light' : 'dark'} />
      </PaperProvider>
    </SafeAreaProvider>
  );
}

export default function App() {
  return (
    <DynamicThemeProvider
      initialConfig={{
        enabled: true,
        fallbackSeedColor: '#6750A4',
        generationStyle: 'TONAL_SPOT',
        autoDetectWallpaper: true,
        respectSystemTheme: true,
      }}
      initialColorScheme="auto"
    >
      <AppContent />
    </DynamicThemeProvider>
  );
}

const styles = StyleSheet.create({
  // Styles can be added here if needed
});
