// @docsMissing
export var AndroidNotificationVisibility;
(function (AndroidNotificationVisibility) {
    AndroidNotificationVisibility[AndroidNotificationVisibility["UNKNOWN"] = 0] = "UNKNOWN";
    AndroidNotificationVisibility[AndroidNotificationVisibility["PUBLIC"] = 1] = "PUBLIC";
    AndroidNotificationVisibility[AndroidNotificationVisibility["PRIVATE"] = 2] = "PRIVATE";
    AndroidNotificationVisibility[AndroidNotificationVisibility["SECRET"] = 3] = "SECRET";
})(AndroidNotificationVisibility || (AndroidNotificationVisibility = {}));
// @docsMissing
export var AndroidAudioContentType;
(function (AndroidAudioContentType) {
    AndroidAudioContentType[AndroidAudioContentType["UNKNOWN"] = 0] = "UNKNOWN";
    AndroidAudioContentType[AndroidAudioContentType["SPEECH"] = 1] = "SPEECH";
    AndroidAudioContentType[AndroidAudioContentType["MUSIC"] = 2] = "MUSIC";
    AndroidAudioContentType[AndroidAudioContentType["MOVIE"] = 3] = "MOVIE";
    AndroidAudioContentType[AndroidAudioContentType["SONIFICATION"] = 4] = "SONIFICATION";
})(AndroidAudioContentType || (AndroidAudioContentType = {}));
// @docsMissing
export var AndroidImportance;
(function (AndroidImportance) {
    AndroidImportance[AndroidImportance["UNKNOWN"] = 0] = "UNKNOWN";
    AndroidImportance[AndroidImportance["UNSPECIFIED"] = 1] = "UNSPECIFIED";
    AndroidImportance[AndroidImportance["NONE"] = 2] = "NONE";
    AndroidImportance[AndroidImportance["MIN"] = 3] = "MIN";
    AndroidImportance[AndroidImportance["LOW"] = 4] = "LOW";
    AndroidImportance[AndroidImportance["DEFAULT"] = 5] = "DEFAULT";
    AndroidImportance[AndroidImportance["HIGH"] = 6] = "HIGH";
    AndroidImportance[AndroidImportance["MAX"] = 7] = "MAX";
})(AndroidImportance || (AndroidImportance = {}));
// @docsMissing
export var AndroidAudioUsage;
(function (AndroidAudioUsage) {
    AndroidAudioUsage[AndroidAudioUsage["UNKNOWN"] = 0] = "UNKNOWN";
    AndroidAudioUsage[AndroidAudioUsage["MEDIA"] = 1] = "MEDIA";
    AndroidAudioUsage[AndroidAudioUsage["VOICE_COMMUNICATION"] = 2] = "VOICE_COMMUNICATION";
    AndroidAudioUsage[AndroidAudioUsage["VOICE_COMMUNICATION_SIGNALLING"] = 3] = "VOICE_COMMUNICATION_SIGNALLING";
    AndroidAudioUsage[AndroidAudioUsage["ALARM"] = 4] = "ALARM";
    AndroidAudioUsage[AndroidAudioUsage["NOTIFICATION"] = 5] = "NOTIFICATION";
    AndroidAudioUsage[AndroidAudioUsage["NOTIFICATION_RINGTONE"] = 6] = "NOTIFICATION_RINGTONE";
    AndroidAudioUsage[AndroidAudioUsage["NOTIFICATION_COMMUNICATION_REQUEST"] = 7] = "NOTIFICATION_COMMUNICATION_REQUEST";
    AndroidAudioUsage[AndroidAudioUsage["NOTIFICATION_COMMUNICATION_INSTANT"] = 8] = "NOTIFICATION_COMMUNICATION_INSTANT";
    AndroidAudioUsage[AndroidAudioUsage["NOTIFICATION_COMMUNICATION_DELAYED"] = 9] = "NOTIFICATION_COMMUNICATION_DELAYED";
    AndroidAudioUsage[AndroidAudioUsage["NOTIFICATION_EVENT"] = 10] = "NOTIFICATION_EVENT";
    AndroidAudioUsage[AndroidAudioUsage["ASSISTANCE_ACCESSIBILITY"] = 11] = "ASSISTANCE_ACCESSIBILITY";
    AndroidAudioUsage[AndroidAudioUsage["ASSISTANCE_NAVIGATION_GUIDANCE"] = 12] = "ASSISTANCE_NAVIGATION_GUIDANCE";
    AndroidAudioUsage[AndroidAudioUsage["ASSISTANCE_SONIFICATION"] = 13] = "ASSISTANCE_SONIFICATION";
    AndroidAudioUsage[AndroidAudioUsage["GAME"] = 14] = "GAME";
})(AndroidAudioUsage || (AndroidAudioUsage = {}));
//# sourceMappingURL=NotificationChannelManager.types.js.map