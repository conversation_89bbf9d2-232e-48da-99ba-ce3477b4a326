{"version": 3, "file": "NotificationPresenterModule.js", "sourceRoot": "", "sources": ["../src/NotificationPresenterModule.ts"], "names": [], "mappings": "AAEA,eAAe;IACb,WAAW,EAAE,GAAG,EAAE,GAAE,CAAC;IACrB,eAAe,EAAE,GAAG,EAAE,GAAE,CAAC;CACK,CAAC", "sourcesContent": ["import { NotificationPresenterModule } from './NotificationPresenterModule.types';\n\nexport default {\n  addListener: () => {},\n  removeListeners: () => {},\n} as NotificationPresenterModule;\n"]}