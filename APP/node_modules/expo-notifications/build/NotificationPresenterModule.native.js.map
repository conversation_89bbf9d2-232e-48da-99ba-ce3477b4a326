{"version": 3, "file": "NotificationPresenterModule.native.js", "sourceRoot": "", "sources": ["../src/NotificationPresenterModule.native.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIxD,eAAe,mBAAmB,CAA8B,2BAA2B,CAAC,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nimport { NotificationPresenterModule } from './NotificationPresenterModule.types';\n\nexport default requireNativeModule<NotificationPresenterModule>('ExpoNotificationPresenter');\n"]}