{"version": 3, "file": "NotificationScheduler.js", "sourceRoot": "", "sources": ["../src/NotificationScheduler.ts"], "names": [], "mappings": "AAEA,eAAe;IACb,WAAW,EAAE,GAAG,EAAE,GAAE,CAAC;IACrB,eAAe,EAAE,GAAG,EAAE,GAAE,CAAC;CACK,CAAC", "sourcesContent": ["import { NotificationSchedulerModule } from './NotificationScheduler.types';\n\nexport default {\n  addListener: () => {},\n  removeListeners: () => {},\n} as NotificationSchedulerModule;\n"]}