{"version": 3, "file": "NotificationsEmitter.js", "sourceRoot": "", "sources": ["../src/NotificationsEmitter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAA0B,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAGpG,OAAO,0BAA0B,MAAM,8BAA8B,CAAC;AACtE,OAAO,EAAE,eAAe,EAAE,uBAAuB,EAAE,MAAM,iCAAiC,CAAC;AAE3F,iCAAiC;AACjC,MAAM,OAAO,GAAG,IAAI,kBAAkB,CAAC,0BAA0B,CAAC,CAAC;AAEnE,MAAM,+BAA+B,GAAG,0BAA0B,CAAC;AACnE,MAAM,6BAA6B,GAAG,wBAAwB,CAAC;AAC/D,MAAM,uCAAuC,GAAG,kCAAkC,CAAC;AACnF,MAAM,qCAAqC,GAAG,gCAAgC,CAAC;AAE/E,eAAe;AACf,MAAM,CAAC,MAAM,yBAAyB,GAAG,4CAA4C,CAAC;AAEtF;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,MAAM,UAAU,+BAA+B,CAC7C,QAAuC;IAEvC,OAAO,OAAO,CAAC,WAAW,CACxB,+BAA+B,EAC/B,CAAC,YAA0B,EAAE,EAAE;QAC7B,MAAM,kBAAkB,GAAG,eAAe,CAAC,YAAY,CAAC,CAAC;QACzD,QAAQ,CAAC,kBAAkB,CAAC,CAAC;IAC/B,CAAC,CACF,CAAC;AACJ,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,+BAA+B,CAAC,QAAoB;IAClE,OAAO,OAAO,CAAC,WAAW,CAAO,6BAA6B,EAAE,QAAQ,CAAC,CAAC;AAC5E,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,MAAM,UAAU,uCAAuC,CACrD,QAA+C;IAE/C,OAAO,OAAO,CAAC,WAAW,CACxB,uCAAuC,EACvC,CAAC,QAA8B,EAAE,EAAE;QACjC,MAAM,cAAc,GAAG,uBAAuB,CAAC,QAAQ,CAAC,CAAC;QACzD,QAAQ,CAAC,cAAc,CAAC,CAAC;IAC3B,CAAC,CACF,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,8BAA8B,CAAC,YAA+B;IAC5E,IAAI,OAAO,YAAY,EAAE,MAAM,KAAK,UAAU,EAAE;QAC9C,YAAY,CAAC,MAAM,EAAE,CAAC;KACvB;SAAM;QACL,MAAM,IAAI,KAAK,CACb,yEAAyE,YAAY,EAAE,CACxF,CAAC;KACH;AACH,CAAC;AAED;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,gCAAgC;IACpD,IAAI,CAAC,0BAA0B,CAAC,gCAAgC,EAAE;QAChE,MAAM,IAAI,mBAAmB,CAAC,mBAAmB,EAAE,kCAAkC,CAAC,CAAC;KACxF;IACD,MAAM,QAAQ,GAAG,MAAM,0BAA0B,CAAC,gCAAgC,EAAE,CAAC;IACrF,MAAM,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC/E,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,CAAC,KAAK,UAAU,kCAAkC;IACtD,IAAI,CAAC,0BAA0B,CAAC,kCAAkC,EAAE;QAClE,MAAM,IAAI,mBAAmB,CAAC,mBAAmB,EAAE,kCAAkC,CAAC,CAAC;KACxF;IACD,MAAM,0BAA0B,CAAC,kCAAkC,EAAE,CAAC;IACtE,wFAAwF;IACxF,OAAO,CAAC,IAAI,CAAC,qCAAqC,EAAE,EAAE,CAAC,CAAC;AAC1D,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,sCAAsC,CAAC,QAAoB;IACzE,OAAO,OAAO,CAAC,WAAW,CAAO,qCAAqC,EAAE,QAAQ,CAAC,CAAC;AACpF,CAAC", "sourcesContent": ["import { LegacyEventEmitter, type EventSubscription, UnavailabilityError } from 'expo-modules-core';\n\nimport { Notification, NotificationResponse } from './Notifications.types';\nimport NotificationsEmitterModule from './NotificationsEmitterModule';\nimport { mapNotification, mapNotificationResponse } from './utils/mapNotificationResponse';\n\n// Web uses SyntheticEventEmitter\nconst emitter = new LegacyEventEmitter(NotificationsEmitterModule);\n\nconst didReceiveNotificationEventName = 'onDidReceiveNotification';\nconst didDropNotificationsEventName = 'onNotificationsDeleted';\nconst didReceiveNotificationResponseEventName = 'onDidReceiveNotificationResponse';\nconst didClearNotificationResponseEventName = 'onDidClearNotificationResponse';\n\n// @docsMissing\nexport const DEFAULT_ACTION_IDENTIFIER = 'expo.modules.notifications.actions.DEFAULT';\n\n/**\n * Listeners registered by this method will be called whenever a notification is received while the app is running.\n * @param listener A function accepting a notification ([`Notification`](#notification)) as an argument.\n * @return An [`EventSubscription`](#eventsubscription) object represents the subscription of the provided listener.\n * @example Registering a notification listener using a React hook:\n * ```jsx\n * import React from 'react';\n * import * as Notifications from 'expo-notifications';\n *\n * export default function App() {\n *   React.useEffect(() => {\n *     const subscription = Notifications.addNotificationReceivedListener(notification => {\n *       console.log(notification);\n *     });\n *     return () => subscription.remove();\n *   }, []);\n *\n *   return (\n *     // Your app content\n *   );\n * }\n * ```\n * @header listen\n */\nexport function addNotificationReceivedListener(\n  listener: (event: Notification) => void\n): EventSubscription {\n  return emitter.addListener<Notification>(\n    didReceiveNotificationEventName,\n    (notification: Notification) => {\n      const mappedNotification = mapNotification(notification);\n      listener(mappedNotification);\n    }\n  );\n}\n\n/**\n * Listeners registered by this method will be called whenever some notifications have been dropped by the server.\n * Applicable only to Firebase Cloud Messaging which we use as a notifications service on Android. It corresponds to `onDeletedMessages()` callback.\n * More information can be found in [Firebase docs](https://firebase.google.com/docs/cloud-messaging/android/receive#override-ondeletedmessages).\n * @param listener A callback function.\n * @return An [`EventSubscription`](#eventsubscription) object represents the subscription of the provided listener.\n * @header listen\n */\nexport function addNotificationsDroppedListener(listener: () => void): EventSubscription {\n  return emitter.addListener<void>(didDropNotificationsEventName, listener);\n}\n\n/**\n * Listeners registered by this method will be called whenever a user interacts with a notification (for example, taps on it).\n * @param listener A function accepting notification response ([`NotificationResponse`](#notificationresponse)) as an argument.\n * @return An [`EventSubscription`](#eventsubscription) object represents the subscription of the provided listener.\n * @example Register a notification responder listener:\n * ```jsx\n * import React from 'react';\n * import { Linking } from 'react-native';\n * import * as Notifications from 'expo-notifications';\n *\n * export default function Container() {\n *   React.useEffect(() => {\n *     const subscription = Notifications.addNotificationResponseReceivedListener(response => {\n *       const url = response.notification.request.content.data.url;\n *       Linking.openURL(url);\n *     });\n *     return () => subscription.remove();\n *   }, []);\n *\n *   return (\n *     // Your app content\n *   );\n * }\n * ```\n * @header listen\n */\nexport function addNotificationResponseReceivedListener(\n  listener: (event: NotificationResponse) => void\n): EventSubscription {\n  return emitter.addListener<NotificationResponse>(\n    didReceiveNotificationResponseEventName,\n    (response: NotificationResponse) => {\n      const mappedResponse = mapNotificationResponse(response);\n      listener(mappedResponse);\n    }\n  );\n}\n\n/**\n * Removes a notification subscription returned by an `addNotificationListener` call.\n * @param subscription A subscription returned by `addNotificationListener` method.\n * @header listen\n */\nexport function removeNotificationSubscription(subscription: EventSubscription) {\n  if (typeof subscription?.remove === 'function') {\n    subscription.remove();\n  } else {\n    throw new Error(\n      `removeNotificationSubscription: Provided value is not a subscription: ${subscription}`\n    );\n  }\n}\n\n/**\n * Gets the notification response that was received most recently\n * (a notification response designates an interaction with a notification, such as tapping on it).\n *\n * - `null` - if no notification response has been received yet\n * - a [`NotificationResponse`](#notificationresponse) object - if a notification response was received\n */\nexport async function getLastNotificationResponseAsync(): Promise<NotificationResponse | null> {\n  if (!NotificationsEmitterModule.getLastNotificationResponseAsync) {\n    throw new UnavailabilityError('ExpoNotifications', 'getLastNotificationResponseAsync');\n  }\n  const response = await NotificationsEmitterModule.getLastNotificationResponseAsync();\n  const mappedResponse = response ? mapNotificationResponse(response) : response;\n  return mappedResponse;\n}\n\n/**\n * Clears the notification response that was received most recently. May be used\n * when an app selects a route based on the notification response, and it is undesirable\n * to continue selecting the route after the response has already been handled.\n *\n * If a component is using the [`useLastNotificationResponse`](#uselastnotificationresponse) hook,\n * this call will also clear the value returned by the hook.\n *\n * @return A promise that resolves if the native call was successful.\n */\nexport async function clearLastNotificationResponseAsync(): Promise<void> {\n  if (!NotificationsEmitterModule.clearLastNotificationResponseAsync) {\n    throw new UnavailabilityError('ExpoNotifications', 'getLastNotificationResponseAsync');\n  }\n  await NotificationsEmitterModule.clearLastNotificationResponseAsync();\n  // Emit event to clear any useLastNotificationResponse hooks, after native call succeeds\n  emitter.emit(didClearNotificationResponseEventName, []);\n}\n\n/**\n * @hidden\n */\nexport function addNotificationResponseClearedListener(listener: () => void): EventSubscription {\n  return emitter.addListener<void>(didClearNotificationResponseEventName, listener);\n}\n"]}