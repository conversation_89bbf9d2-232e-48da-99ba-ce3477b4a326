{"version": 3, "file": "NotificationsEmitterModule.native.js", "sourceRoot": "", "sources": ["../src/NotificationsEmitterModule.native.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIxD,eAAe,mBAAmB,CAA6B,0BAA0B,CAAC,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nimport { NotificationsEmitterModule } from './NotificationsEmitterModule.types';\n\nexport default requireNativeModule<NotificationsEmitterModule>('ExpoNotificationsEmitter');\n"]}