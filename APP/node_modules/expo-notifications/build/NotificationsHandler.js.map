{"version": 3, "file": "NotificationsHandler.js", "sourceRoot": "", "sources": ["../src/NotificationsHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,kBAAkB,EAElB,UAAU,EACV,mBAAmB,GACpB,MAAM,mBAAmB,CAAC;AAG3B,OAAO,0BAA0B,MAAM,8BAA8B,CAAC;AAEtE;;GAEG;AACH,MAAM,OAAO,wBAAyB,SAAQ,UAAU;IACtD,IAAI,CAA6C;IACjD,YAAY,cAAsB,EAAE,YAA0B;QAC5D,KAAK,CAAC,0BAA0B,EAAE,0CAA0C,cAAc,GAAG,CAAC,CAAC;QAC/F,IAAI,CAAC,IAAI,GAAG,EAAE,EAAE,EAAE,cAAc,EAAE,YAAY,EAAE,CAAC;IACnD,CAAC;CACF;AAgCD,iCAAiC;AACjC,MAAM,mBAAmB,GAAG,IAAI,kBAAkB,CAAC,0BAA0B,CAAC,CAAC;AAE/E,MAAM,2BAA2B,GAAG,sBAAsB,CAAC;AAC3D,MAAM,kCAAkC,GAAG,6BAA6B,CAAC;AAEzE,IAAI,kBAAkB,GAA6B,IAAI,CAAC;AACxD,IAAI,yBAAyB,GAA6B,IAAI,CAAC;AAE/D;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,MAAM,UAAU,sBAAsB,CAAC,OAAmC;IACxE,IAAI,kBAAkB,EAAE;QACtB,kBAAkB,CAAC,MAAM,EAAE,CAAC;QAC5B,kBAAkB,GAAG,IAAI,CAAC;KAC3B;IACD,IAAI,yBAAyB,EAAE;QAC7B,yBAAyB,CAAC,MAAM,EAAE,CAAC;QACnC,yBAAyB,GAAG,IAAI,CAAC;KAClC;IAED,IAAI,OAAO,EAAE;QACX,kBAAkB,GAAG,mBAAmB,CAAC,WAAW,CAClD,2BAA2B,EAC3B,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE;YAC7B,IAAI,CAAC,0BAA0B,CAAC,uBAAuB,EAAE;gBACvD,OAAO,CAAC,WAAW,EAAE,CACnB,EAAE,EACF,IAAI,mBAAmB,CAAC,eAAe,EAAE,yBAAyB,CAAC,CACpE,CAAC;gBACF,OAAO;aACR;YAED,IAAI;gBACF,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;gBAChE,MAAM,0BAA0B,CAAC,uBAAuB,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;gBACvE,OAAO,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC;aAC7B;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;aAClC;QACH,CAAC,CACF,CAAC;QAEF,yBAAyB,GAAG,mBAAmB,CAAC,WAAW,CACzD,kCAAkC,EAClC,CAAC,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,CACvB,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,IAAI,wBAAwB,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC,CAC5E,CAAC;KACH;AACH,CAAC", "sourcesContent": ["import {\n  LegacyEventEmitter,\n  type EventSubscription,\n  CodedError,\n  UnavailabilityError,\n} from 'expo-modules-core';\n\nimport { Notification, NotificationBehavior } from './Notifications.types';\nimport NotificationsHandlerModule from './NotificationsHandlerModule';\n\n/**\n * @hidden\n */\nexport class NotificationTimeoutError extends CodedError {\n  info: { notification: Notification; id: string };\n  constructor(notificationId: string, notification: Notification) {\n    super('ERR_NOTIFICATION_TIMEOUT', `Notification handling timed out for ID ${notificationId}.`);\n    this.info = { id: notificationId, notification };\n  }\n}\n\n// @docsMissing\nexport type NotificationHandlingError = NotificationTimeoutError | Error;\n\nexport interface NotificationHandler {\n  /**\n   * A function accepting an incoming notification returning a `Promise` resolving to a behavior ([`NotificationBehavior`](#notificationbehavior))\n   * applicable to the notification\n   * @param notification An object representing the notification.\n   */\n  handleNotification: (notification: Notification) => Promise<NotificationBehavior>;\n  /**\n   * A function called whenever an incoming notification is handled successfully.\n   * @param notificationId Identifier of the notification.\n   */\n  handleSuccess?: (notificationId: string) => void;\n  /**\n   * A function called whenever calling `handleNotification()` for an incoming notification fails.\n   * @param notificationId Identifier of the notification.\n   * @param error An error which occurred in form of `NotificationHandlingError` object.\n   */\n  handleError?: (notificationId: string, error: NotificationHandlingError) => void;\n}\n\ntype HandleNotificationEvent = {\n  id: string;\n  notification: Notification;\n};\n\ntype HandleNotificationTimeoutEvent = HandleNotificationEvent;\n\n// Web uses SyntheticEventEmitter\nconst notificationEmitter = new LegacyEventEmitter(NotificationsHandlerModule);\n\nconst handleNotificationEventName = 'onHandleNotification';\nconst handleNotificationTimeoutEventName = 'onHandleNotificationTimeout';\n\nlet handleSubscription: EventSubscription | null = null;\nlet handleTimeoutSubscription: EventSubscription | null = null;\n\n/**\n * When a notification is received while the app is running, using this function you can set a callback that will decide\n * whether the notification should be shown to the user or not.\n *\n * When a notification is received, `handleNotification` is called with the incoming notification as an argument.\n * The function should respond with a behavior object within 3 seconds, otherwise, the notification will be discarded.\n * If the notification is handled successfully, `handleSuccess` is called with the identifier of the notification,\n * otherwise (or on timeout) `handleError` will be called.\n *\n * The default behavior when the handler is not set or does not respond in time is not to show the notification.\n * @param handler A single parameter which should be either `null` (if you want to clear the handler) or a [`NotificationHandler`](#notificationhandler) object.\n *\n * @example Implementing a notification handler that always shows the notification when it is received.\n * ```jsx\n * import * as Notifications from 'expo-notifications';\n *\n * Notifications.setNotificationHandler({\n *   handleNotification: async () => ({\n *     shouldShowAlert: true,\n *     shouldPlaySound: false,\n *     shouldSetBadge: false,\n *   }),\n * });\n * ```\n * @header inForeground\n */\nexport function setNotificationHandler(handler: NotificationHandler | null): void {\n  if (handleSubscription) {\n    handleSubscription.remove();\n    handleSubscription = null;\n  }\n  if (handleTimeoutSubscription) {\n    handleTimeoutSubscription.remove();\n    handleTimeoutSubscription = null;\n  }\n\n  if (handler) {\n    handleSubscription = notificationEmitter.addListener<HandleNotificationEvent>(\n      handleNotificationEventName,\n      async ({ id, notification }) => {\n        if (!NotificationsHandlerModule.handleNotificationAsync) {\n          handler.handleError?.(\n            id,\n            new UnavailabilityError('Notifications', 'handleNotificationAsync')\n          );\n          return;\n        }\n\n        try {\n          const behavior = await handler.handleNotification(notification);\n          await NotificationsHandlerModule.handleNotificationAsync(id, behavior);\n          handler.handleSuccess?.(id);\n        } catch (error) {\n          handler.handleError?.(id, error);\n        }\n      }\n    );\n\n    handleTimeoutSubscription = notificationEmitter.addListener<HandleNotificationTimeoutEvent>(\n      handleNotificationTimeoutEventName,\n      ({ id, notification }) =>\n        handler.handleError?.(id, new NotificationTimeoutError(id, notification))\n    );\n  }\n}\n"]}