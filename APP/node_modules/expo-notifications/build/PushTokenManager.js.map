{"version": 3, "file": "PushTokenManager.js", "sourceRoot": "", "sources": ["../src/PushTokenManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAI7C,IAAI,mBAAmB,GAAG,KAAK,CAAC;AAEhC,eAAe;IACb,WAAW,EAAE,GAAG,EAAE;QAChB,IAAI,CAAC,mBAAmB,EAAE;YACxB,OAAO,CAAC,IAAI,CACV,sFAAsF,QAAQ,CAAC,EAAE,0CAA0C,CAC5I,CAAC;YACF,mBAAmB,GAAG,IAAI,CAAC;SAC5B;IACH,CAAC;IACD,eAAe,EAAE,GAAG,EAAE,GAAE,CAAC;CACA,CAAC", "sourcesContent": ["import { Platform } from 'expo-modules-core';\n\nimport { PushTokenManagerModule } from './PushTokenManager.types';\n\nlet warningHasBeenShown = false;\n\nexport default {\n  addListener: () => {\n    if (!warningHasBeenShown) {\n      console.warn(\n        `[expo-notifications] Listening to push token changes is not yet fully supported on ${Platform.OS}. Adding a listener will have no effect.`\n      );\n      warningHasBeenShown = true;\n    }\n  },\n  removeListeners: () => {},\n} as PushTokenManagerModule;\n"]}