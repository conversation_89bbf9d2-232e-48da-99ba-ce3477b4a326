{"version": 3, "file": "PushTokenManager.native.js", "sourceRoot": "", "sources": ["../src/PushTokenManager.native.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIxD,eAAe,mBAAmB,CAAyB,sBAAsB,CAAC,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nimport { PushTokenManagerModule } from './PushTokenManager.types';\n\nexport default requireNativeModule<PushTokenManagerModule>('ExpoPushTokenManager');\n"]}