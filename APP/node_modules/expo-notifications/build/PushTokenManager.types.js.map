{"version": 3, "file": "PushTokenManager.types.js", "sourceRoot": "", "sources": ["../src/PushTokenManager.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ProxyNativeModule } from 'expo-modules-core';\n\nexport interface PushTokenManagerModule extends ProxyNativeModule {\n  getDevicePushTokenAsync?: () => Promise<string>;\n  unregisterForNotificationsAsync?: () => Promise<void>;\n}\n"]}