{"version": 3, "file": "ServerRegistrationModule.types.js", "sourceRoot": "", "sources": ["../src/ServerRegistrationModule.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ProxyNativeModule } from 'expo-modules-core';\n\nexport interface ServerRegistrationModule extends ProxyNativeModule {\n  getInstallationIdAsync?: () => Promise<string>;\n  getRegistrationInfoAsync?: () => Promise<string | undefined | null>;\n  setRegistrationInfoAsync?: (registrationInfo: string | null) => Promise<void>;\n}\n"]}