{"version": 3, "file": "deleteNotificationChannelAsync.android.js", "sourceRoot": "", "sources": ["../src/deleteNotificationChannelAsync.android.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,0BAA0B,MAAM,8BAA8B,CAAC;AAEtE,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,8BAA8B,CAAC,SAAiB;IAC5E,IAAI,CAAC,0BAA0B,CAAC,8BAA8B,EAAE;QAC9D,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,gCAAgC,CAAC,CAAC;KAClF;IAED,OAAO,MAAM,0BAA0B,CAAC,8BAA8B,CAAC,SAAS,CAAC,CAAC;AACpF,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationChannelManager from './NotificationChannelManager';\n\nexport default async function deleteNotificationChannelAsync(channelId: string): Promise<void> {\n  if (!NotificationChannelManager.deleteNotificationChannelAsync) {\n    throw new UnavailabilityError('Notifications', 'deleteNotificationChannelAsync');\n  }\n\n  return await NotificationChannelManager.deleteNotificationChannelAsync(channelId);\n}\n"]}