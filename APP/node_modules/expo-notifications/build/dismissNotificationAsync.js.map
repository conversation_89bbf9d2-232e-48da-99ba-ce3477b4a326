{"version": 3, "file": "dismissNotificationAsync.js", "sourceRoot": "", "sources": ["../src/dismissNotificationAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,qBAAqB,MAAM,+BAA+B,CAAC;AAElE;;;;;GAKG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,wBAAwB,CACpD,sBAA8B;IAE9B,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,EAAE;QACnD,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,0BAA0B,CAAC,CAAC;KAC5E;IAED,OAAO,MAAM,qBAAqB,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,CAAC;AACtF,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationPresenter from './NotificationPresenterModule';\n\n/**\n * Removes notification displayed in the notification tray (Notification Center).\n * @param notificationIdentifier The notification identifier, obtained either via `setNotificationHandler` method or in the listener added with `addNotificationReceivedListener`.\n * @return A Promise which resolves once the request to dismiss the notification is successfully dispatched to the notifications manager.\n * @header dismiss\n */\nexport default async function dismissNotificationAsync(\n  notificationIdentifier: string\n): Promise<void> {\n  if (!NotificationPresenter.dismissNotificationAsync) {\n    throw new UnavailabilityError('Notifications', 'dismissNotificationAsync');\n  }\n\n  return await NotificationPresenter.dismissNotificationAsync(notificationIdentifier);\n}\n"]}