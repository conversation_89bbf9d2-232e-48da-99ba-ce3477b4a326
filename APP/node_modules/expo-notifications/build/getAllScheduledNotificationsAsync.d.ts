import { NotificationRequest } from './Notifications.types';
/**
 * Fetches information about all scheduled notifications.
 * @return Returns a Promise resolving to an array of objects conforming to the [`Notification`](#notification) interface.
 * @header schedule
 */
export default function getAllScheduledNotificationsAsync(): Promise<NotificationRequest[]>;
//# sourceMappingURL=getAllScheduledNotificationsAsync.d.ts.map