{"version": 3, "file": "getNotificationChannelAsync.js", "sourceRoot": "", "sources": ["../src/getNotificationChannelAsync.ts"], "names": [], "mappings": "AAEA;;;;;;;GAOG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,2BAA2B,CACvD,SAAiB;IAEjB,OAAO,CAAC,KAAK,CAAC,6DAA6D,CAAC,CAAC;IAC7E,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import { NotificationChannel } from './NotificationChannelManager.types';\n\n/**\n * Fetches information about a single notification channel.\n * @param channelId The channel's identifier.\n * @return A Promise which resolves to the channel object (of type [`NotificationChannel`](#notificationchannel)) or to `null`\n * if there was no channel found for this identifier. On platforms that do not support notification channels, it will always resolve to `null`.\n * @platform android\n * @header channels\n */\nexport default async function getNotificationChannelAsync(\n  channelId: string\n): Promise<NotificationChannel | null> {\n  console.debug('Notification channels feature is only supported on Android.');\n  return null;\n}\n"]}