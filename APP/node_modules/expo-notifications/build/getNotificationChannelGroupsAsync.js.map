{"version": 3, "file": "getNotificationChannelGroupsAsync.js", "sourceRoot": "", "sources": ["../src/getNotificationChannelGroupsAsync.ts"], "names": [], "mappings": "AAEA;;;;;;GAMG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,iCAAiC;IAG7D,OAAO,CAAC,KAAK,CAAC,6DAA6D,CAAC,CAAC;IAC7E,OAAO,EAAE,CAAC;AACZ,CAAC", "sourcesContent": ["import { NotificationChannelGroup } from './NotificationChannelGroupManager.types';\n\n/**\n * Fetches information about all known notification channel groups.\n * @return A Promise which resoles to an array of channel groups. On platforms that do not support notification channel groups,\n * it will always resolve to an empty array.\n * @platform android\n * @header channels\n */\nexport default async function getNotificationChannelGroupsAsync(): Promise<\n  NotificationChannelGroup[]\n> {\n  console.debug('Notification channels feature is only supported on Android.');\n  return [];\n}\n"]}