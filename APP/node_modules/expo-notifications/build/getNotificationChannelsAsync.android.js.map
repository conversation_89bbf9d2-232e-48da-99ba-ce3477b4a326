{"version": 3, "file": "getNotificationChannelsAsync.android.js", "sourceRoot": "", "sources": ["../src/getNotificationChannelsAsync.android.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,0BAA0B,MAAM,8BAA8B,CAAC;AAGtE,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,4BAA4B;IACxD,IAAI,CAAC,0BAA0B,CAAC,4BAA4B,EAAE;QAC5D,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,8BAA8B,CAAC,CAAC;KAChF;IACD,OAAO,CAAC,MAAM,0BAA0B,CAAC,4BAA4B,EAAE,CAAC,IAAI,EAAE,CAAC;AACjF,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport NotificationChannelManager from './NotificationChannelManager';\nimport { NotificationChannel } from './NotificationChannelManager.types';\n\nexport default async function getNotificationChannelsAsync(): Promise<NotificationChannel[]> {\n  if (!NotificationChannelManager.getNotificationChannelsAsync) {\n    throw new UnavailabilityError('Notifications', 'getNotificationChannelsAsync');\n  }\n  return (await NotificationChannelManager.getNotificationChannelsAsync()) ?? [];\n}\n"]}