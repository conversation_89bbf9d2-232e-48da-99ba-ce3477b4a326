{"version": 3, "file": "getNotificationChannelsAsync.js", "sourceRoot": "", "sources": ["../src/getNotificationChannelsAsync.ts"], "names": [], "mappings": "AAEA;;;;;;GAMG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,4BAA4B;IACxD,OAAO,CAAC,KAAK,CAAC,6DAA6D,CAAC,CAAC;IAC7E,OAAO,EAAE,CAAC;AACZ,CAAC", "sourcesContent": ["import { NotificationChannel } from './NotificationChannelManager.types';\n\n/**\n * Fetches information about all known notification channels.\n * @return A Promise which resolves to an array of channels. On platforms that do not support notification channels,\n * it will always resolve to an empty array.\n * @platform android\n * @header channels\n */\nexport default async function getNotificationChannelsAsync(): Promise<NotificationChannel[]> {\n  console.debug('Notification channels feature is only supported on Android.');\n  return [];\n}\n"]}