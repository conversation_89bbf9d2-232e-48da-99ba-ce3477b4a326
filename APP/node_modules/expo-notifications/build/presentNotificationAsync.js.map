{"version": 3, "file": "presentNotificationAsync.js", "sourceRoot": "", "sources": ["../src/presentNotificationAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AAE9D,OAAO,qBAAqB,MAAM,+BAA+B,CAAC;AAGlE,IAAI,mBAAmB,GAAG,KAAK,CAAC;AAEhC;;;;;;;;GAQG;AACH,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,wBAAwB,CACpD,OAAiC,EACjC,aAAqB,IAAI,CAAC,EAAE,EAAE;IAE9B,IAAI,OAAO,IAAI,CAAC,mBAAmB,EAAE;QACnC,OAAO,CAAC,IAAI,CACV,qMAAqM,CACtM,CAAC;QACF,mBAAmB,GAAG,IAAI,CAAC;KAC5B;IAED,IAAI,CAAC,qBAAqB,CAAC,wBAAwB,EAAE;QACnD,MAAM,IAAI,mBAAmB,CAAC,eAAe,EAAE,0BAA0B,CAAC,CAAC;KAC5E;IAED,OAAO,MAAM,qBAAqB,CAAC,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AACnF,CAAC", "sourcesContent": ["import { UnavailabilityError, uuid } from 'expo-modules-core';\n\nimport NotificationPresenter from './NotificationPresenterModule';\nimport { NotificationContentInput } from './Notifications.types';\n\nlet warningMessageShown = false;\n\n/**\n * Schedules a notification for immediate trigger.\n * @param content An object representing the notification content.\n * @param identifier\n * @return It returns a Promise resolving with the notification's identifier once the notification is successfully scheduled for immediate display.\n * @header schedule\n * @deprecated This method has been deprecated in favor of using an explicit `NotificationHandler` and the [`scheduleNotificationAsync`](#schedulenotificationasyncrequest) method.\n * More information can be found in our [FYI document](https://expo.fyi/presenting-notifications-deprecated).\n */\nexport default async function presentNotificationAsync(\n  content: NotificationContentInput,\n  identifier: string = uuid.v4()\n): Promise<string> {\n  if (__DEV__ && !warningMessageShown) {\n    console.warn(\n      '`presentNotificationAsync` has been deprecated in favor of using `scheduleNotificationAsync` + an explicit notification handler. Read more at https://expo.fyi/presenting-notifications-deprecated.'\n    );\n    warningMessageShown = true;\n  }\n\n  if (!NotificationPresenter.presentNotificationAsync) {\n    throw new UnavailabilityError('Notifications', 'presentNotificationAsync');\n  }\n\n  return await NotificationPresenter.presentNotificationAsync(identifier, content);\n}\n"]}