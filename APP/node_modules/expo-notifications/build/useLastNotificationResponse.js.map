{"version": 3, "file": "useLastNotificationResponse.js", "sourceRoot": "", "sources": ["../src/useLastNotificationResponse.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AAGlD,OAAO,EACL,uCAAuC,EACvC,sCAAsC,EACtC,gCAAgC,GACjC,MAAM,wBAAwB,CAAC;AAEhC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqCG;AACH,MAAM,CAAC,OAAO,UAAU,2BAA2B;IACjD,MAAM,CAAC,wBAAwB,EAAE,2BAA2B,CAAC,GAC3D,QAAQ,CAA4B,SAAS,CAAC,CAAC;IAEjD,oFAAoF;IACpF,yCAAyC;IACzC,MAAM,mBAAmB,GAAG,CAC1B,YAAuC,EACvC,WAAsC,EACtC,EAAE;QACF,+DAA+D;QAC/D,IAAI,CAAC,WAAW,EAAE;YAChB,OAAO,YAAY,CAAC;SACrB;QACD,8FAA8F;QAC9F,IAAI,CAAC,YAAY,EAAE;YACjB,OAAO,WAAW,CAAC;SACpB;QACD,OAAO,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU;YACjD,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU;YAC3C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,YAAY,CAAC;IACnB,CAAC,CAAC;IAEF,yEAAyE;IACzE,eAAe,CAAC,GAAG,EAAE;QACnB,2FAA2F;QAC3F,sBAAsB;QACtB,gCAAgC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE,CACrD,2BAA2B,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,mBAAmB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,CAC3F,CAAC;QAEF,kFAAkF;QAClF,MAAM,YAAY,GAAG,uCAAuC,CAAC,CAAC,QAAQ,EAAE,EAAE,CACxE,2BAA2B,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,mBAAmB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,CAC3F,CAAC;QACF,MAAM,yBAAyB,GAAG,sCAAsC,CAAC,GAAG,EAAE;YAC5E,2BAA2B,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QACH,OAAO,GAAG,EAAE;YACV,YAAY,CAAC,MAAM,EAAE,CAAC;YACtB,yBAAyB,CAAC,MAAM,EAAE,CAAC;QACrC,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,wBAAwB,CAAC;AAClC,CAAC", "sourcesContent": ["import { useLayoutEffect, useState } from 'react';\n\nimport { MaybeNotificationResponse } from './Notifications.types';\nimport {\n  addNotificationResponseReceivedListener,\n  addNotificationResponseClearedListener,\n  getLastNotificationResponseAsync,\n} from './NotificationsEmitter';\n\n/**\n * A React hook which returns the notification response that was received most recently\n * (a notification response designates an interaction with a notification, such as tapping on it).\n *\n * To clear the last notification response, use [`clearLastNotificationResponseAsync()`](#notificationsclearlastnotificationresponseasync).\n *\n * > If you don't want to use a hook, you can use `Notifications.getLastNotificationResponseAsync()` instead.\n *\n * @return The hook may return one of these three types/values:\n * - `undefined` - until we're sure of what to return,\n * - `null` - if no notification response has been received yet,\n * - a [`NotificationResponse`](#notificationresponse) object - if a notification response was received.\n *\n * @example\n * Responding to a notification tap by opening a URL that could be put into the notification's `data`\n * (opening the URL is your responsibility and is not a part of the `expo-notifications` API):\n * ```jsx\n * import * as Notifications from 'expo-notifications';\n * import { Linking } from 'react-native';\n *\n * export default function App() {\n *   const lastNotificationResponse = Notifications.useLastNotificationResponse();\n *   React.useEffect(() => {\n *     if (\n *       lastNotificationResponse &&\n *       lastNotificationResponse.notification.request.content.data.url &&\n *       lastNotificationResponse.actionIdentifier === Notifications.DEFAULT_ACTION_IDENTIFIER\n *     ) {\n *       Linking.openURL(lastNotificationResponse.notification.request.content.data.url);\n *     }\n *   }, [lastNotificationResponse]);\n *   return (\n *     // Your app content\n *   );\n * }\n * ```\n * @header listen\n */\nexport default function useLastNotificationResponse() {\n  const [lastNotificationResponse, setLastNotificationResponse] =\n    useState<MaybeNotificationResponse>(undefined);\n\n  // Pure function that returns the new response if it is different from the previous,\n  // otherwise return the previous response\n  const newResponseIfNeeded = (\n    prevResponse: MaybeNotificationResponse,\n    newResponse: MaybeNotificationResponse\n  ) => {\n    // If the new response is undefined or null, no need for update\n    if (!newResponse) {\n      return prevResponse;\n    }\n    // If the previous response is undefined or null and the new response is not, we should update\n    if (!prevResponse) {\n      return newResponse;\n    }\n    return prevResponse.notification.request.identifier !==\n      newResponse.notification.request.identifier\n      ? newResponse\n      : prevResponse;\n  };\n\n  // useLayoutEffect ensures the listener is registered as soon as possible\n  useLayoutEffect(() => {\n    // Get the last response first, in case it was set earlier (even in native code on startup)\n    // before this renders\n    getLastNotificationResponseAsync?.().then((response) =>\n      setLastNotificationResponse((prevResponse) => newResponseIfNeeded(prevResponse, response))\n    );\n\n    // Set up listener for responses that come in, and set the last response if needed\n    const subscription = addNotificationResponseReceivedListener((response) =>\n      setLastNotificationResponse((prevResponse) => newResponseIfNeeded(prevResponse, response))\n    );\n    const clearResponseSubscription = addNotificationResponseClearedListener(() => {\n      setLastNotificationResponse(undefined);\n    });\n    return () => {\n      subscription.remove();\n      clearResponseSubscription.remove();\n    };\n  }, []);\n\n  return lastNotificationResponse;\n}\n"]}