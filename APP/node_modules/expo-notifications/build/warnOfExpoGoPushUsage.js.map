{"version": 3, "file": "warnOfExpoGoPushUsage.js", "sourceRoot": "", "sources": ["../src/warnOfExpoGoPushUsage.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,MAAM,CAAC;AAEzC,IAAI,OAAO,GAAG,KAAK,CAAC;AAEpB,MAAM,CAAC,MAAM,qBAAqB,GAAG,GAAG,EAAE;IACxC,IAAI,OAAO,IAAI,iBAAiB,EAAE,IAAI,CAAC,OAAO,EAAE;QAC9C,OAAO,GAAG,IAAI,CAAC;QACf,OAAO,CAAC,IAAI,CACV,mQAAmQ,CACpQ,CAAC;KACH;AACH,CAAC,CAAC", "sourcesContent": ["import { isRunningInExpoGo } from 'expo';\n\nlet didWarn = false;\n\nexport const warnOfExpoGoPushUsage = () => {\n  if (__DEV__ && isRunningInExpoGo() && !didWarn) {\n    didWarn = true;\n    console.warn(\n      `expo-notifications: Push notifications (remote notifications) functionality provided by expo-notifications will be removed from Expo Go in SDK 53. Instead, use a development build. Read more at https://docs.expo.dev/develop/development-builds/introduction/.`\n    );\n  }\n};\n"]}