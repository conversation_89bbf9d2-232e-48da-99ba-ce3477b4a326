# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.0](https://github.com/ljharb/safe-regex-test/compare/v1.0.3...v1.1.0) - 2024-12-12

### Commits

- [actions] split out node 10-20, and 20+ [`b4a46bb`](https://github.com/ljharb/safe-regex-test/commit/b4a46bb30542251df8051aec52561ce2bb162f85)
- [New] add types [`5cb24eb`](https://github.com/ljharb/safe-regex-test/commit/5cb24eb6d074fdae200446e172f1ab485460c34e)
- [<PERSON>] update `@ljharb/eslint-config`, `auto-changelog`, `es-value-fixtures`, `object-inspect`, `tape` [`e225ca0`](https://github.com/ljharb/safe-regex-test/commit/e225ca081b77ea105b5e913a00473066efeb471d)
- [Refactor] use `call-bound` directly [`9be3cd2`](https://github.com/ljharb/safe-regex-test/commit/9be3cd2f390f23c0075ec93e5abdace0ee5d9d9d)
- [Deps] update `call-bind`, `is-regex` [`524b736`](https://github.com/ljharb/safe-regex-test/commit/524b73677e16de9dde27dfb8f30ac4760071bbda)
- [Tests] replace `aud` with `npm audit` [`f3cd537`](https://github.com/ljharb/safe-regex-test/commit/f3cd5379e48152daeb51fdd09e15d3ec74797761)
- [Dev Deps] add missing peer dep [`14da559`](https://github.com/ljharb/safe-regex-test/commit/14da559a0620b99a262ab9610dba13720915360f)

## [v1.0.3](https://github.com/ljharb/safe-regex-test/compare/v1.0.2...v1.0.3) - 2024-02-06

### Commits

- [Refactor] use `es-errors`, so things that only need those do not need `get-intrinsic` [`d6ba2f9`](https://github.com/ljharb/safe-regex-test/commit/d6ba2f948c679c3104ce6d6c897bedff0f1c5b74)
- [Deps] update `call-bind`, `get-intrinsic` [`5a3b1d7`](https://github.com/ljharb/safe-regex-test/commit/5a3b1d755e46f010e6930f15ec30eae023feffd3)
- [Dev Deps] update `tape` [`75fb719`](https://github.com/ljharb/safe-regex-test/commit/75fb71937c5daf1326052c59d6f251f439cd9332)

## [v1.0.2](https://github.com/ljharb/safe-regex-test/compare/v1.0.1...v1.0.2) - 2024-01-11

### Commits

- [meta] package.json `sideEffects` should be boolean [`094bb88`](https://github.com/ljharb/safe-regex-test/commit/094bb88d93ce25e26d20f5badee4e45acf0e3ac5)

## [v1.0.1](https://github.com/ljharb/safe-regex-test/compare/v1.0.0...v1.0.1) - 2024-01-09

### Commits

- [Tests] add nyc for coverage [`7e3f525`](https://github.com/ljharb/safe-regex-test/commit/7e3f5254efdf0979f72492f0e7f52a3a9814591f)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `npmignore`, `object-inspect`, `tape` [`e7e0169`](https://github.com/ljharb/safe-regex-test/commit/e7e016949b78602f24debc1185c26f33cc4e9d1b)
- [actions] update rebase action [`2962694`](https://github.com/ljharb/safe-regex-test/commit/2962694bce7ffa278e873911072c11119bb3a608)
- [readme] add testing badges; remove david-dm badges [`e9dfd83`](https://github.com/ljharb/safe-regex-test/commit/e9dfd830655ac702ac7b7947f7076bb524994968)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`496fe99`](https://github.com/ljharb/safe-regex-test/commit/496fe99aa66f20cadb1cf79f6d479b87ae95b620)
- [Deps] update `call-bind`, `get-intrinsic` [`d94c5ba`](https://github.com/ljharb/safe-regex-test/commit/d94c5badd0362df8ff7ced38b50d20550fd629c1)
- [meta] add missing `engines.node` [`f3d4711`](https://github.com/ljharb/safe-regex-test/commit/f3d4711a51b21330e34f5f27e45452fbdb924715)
- [Deps] update `get-intrinsic` [`0eeedd7`](https://github.com/ljharb/safe-regex-test/commit/0eeedd74d0313fab9b0718895c02905f702ecb4d)
- [meta] add `sideEffects` flag [`fe1655f`](https://github.com/ljharb/safe-regex-test/commit/fe1655f16449208d987d9f4b7dafb15564ca80f7)

## v1.0.0 - 2022-09-22

### Commits

- Initial implementation, tests, readme [`0273e9f`](https://github.com/ljharb/safe-regex-test/commit/0273e9f96f4b09df413523f4faacc8ae9ac5e6cb)
- Initial commit [`b6c1edf`](https://github.com/ljharb/safe-regex-test/commit/b6c1edf740e6105fb71c34c1c69fadd837e8f7ab)
- npm init [`c7f5765`](https://github.com/ljharb/safe-regex-test/commit/c7f576580607b16458b5a16e6bfa3b639e49c6bd)
- Only apps should have lockfiles [`1162bf0`](https://github.com/ljharb/safe-regex-test/commit/1162bf011835040f7e2c9936734294b2d98536bf)
