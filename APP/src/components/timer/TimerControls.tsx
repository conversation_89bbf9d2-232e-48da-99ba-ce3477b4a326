import React from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  Animated,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypographyVariants } from '../../constants/expressiveTheme';
import { TimerStatus, TimerMode } from './TimerCore';

// ===========================================
// ⏯️ TIMER CONTROLS COMPONENT
// ===========================================

interface TimerControlsProps {
  status: TimerStatus;
  mode: TimerMode;
  onStart: () => void;
  onPause: () => void;
  onReset: () => void;
  onModeToggle: () => void;
  disabled?: boolean;
}

export const TimerControls: React.FC<TimerControlsProps> = ({
  status,
  mode,
  onStart,
  onPause,
  onReset,
  onModeToggle,
  disabled = false,
}) => {
  const theme = useDynamicTheme();
  const scaleAnim = React.useRef(new Animated.Value(1)).current;

  const handlePrimaryPress = () => {
    // Animate button press
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    // Execute action
    if (status === 'running') {
      onPause();
    } else {
      onStart();
    }
  };

  const getPrimaryButtonText = () => {
    switch (status) {
      case 'idle':
        return 'START';
      case 'running':
        return 'PAUSE';
      case 'paused':
        return 'RESUME';
      default:
        return 'START';
    }
  };

  const getPrimaryButtonIcon = () => {
    switch (status) {
      case 'running':
        return '⏸️';
      case 'paused':
        return '▶️';
      default:
        return '▶️';
    }
  };

  const styles = StyleSheet.create({
    container: {
      alignItems: 'center',
      gap: 24,
    },
    primaryButtonContainer: {
      alignItems: 'center',
    },
    primaryButton: {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      elevation: 8,
      shadowColor: theme.colors.primary,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
    },
    primaryButtonRunning: {
      backgroundColor: theme.colors.tertiary,
      shadowColor: theme.colors.tertiary,
    },
    primaryButtonDisabled: {
      backgroundColor: theme.colors.surfaceDisabled,
      shadowOpacity: 0.1,
    },
    primaryButtonIcon: {
      fontSize: 32,
      marginBottom: 4,
    },
    primaryButtonText: {
      ...ExpressiveTypographyVariants.buttonPrimary,
      color: theme.colors.onPrimary,
      fontSize: 12,
      fontWeight: '600',
    },
    primaryButtonTextDisabled: {
      color: theme.colors.onSurfaceDisabled,
    },
    secondaryControls: {
      flexDirection: 'row',
      gap: 16,
      alignItems: 'center',
    },
    secondaryButton: {
      width: 56,
      height: 56,
      borderRadius: 28,
      backgroundColor: theme.colors.secondaryContainer,
      justifyContent: 'center',
      alignItems: 'center',
      elevation: 2,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    secondaryButtonDisabled: {
      backgroundColor: theme.colors.surfaceDisabled,
      shadowOpacity: 0,
    },
    secondaryButtonIcon: {
      fontSize: 20,
    },
    secondaryButtonText: {
      ...ExpressiveTypographyVariants.buttonSecondary,
      color: theme.colors.onSecondaryContainer,
      fontSize: 10,
      marginTop: 2,
    },
    secondaryButtonTextDisabled: {
      color: theme.colors.onSurfaceDisabled,
    },
    modeToggle: {
      backgroundColor: theme.colors.tertiaryContainer,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      marginBottom: 16,
    },
    modeToggleText: {
      ...ExpressiveTypographyVariants.labelMedium,
      color: theme.colors.onTertiaryContainer,
      fontWeight: '600',
    },
    statusIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
      marginTop: 16,
    },
    statusDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
    },
    statusDotIdle: {
      backgroundColor: theme.colors.outline,
    },
    statusDotRunning: {
      backgroundColor: theme.colors.primary,
    },
    statusDotPaused: {
      backgroundColor: theme.colors.tertiary,
    },
    statusText: {
      ...ExpressiveTypographyVariants.labelSmall,
      color: theme.colors.onSurfaceVariant,
      textTransform: 'uppercase',
    },
  });

  const getStatusDotStyle = () => {
    switch (status) {
      case 'running':
        return styles.statusDotRunning;
      case 'paused':
        return styles.statusDotPaused;
      default:
        return styles.statusDotIdle;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'running':
        return 'Running';
      case 'paused':
        return 'Paused';
      default:
        return 'Ready';
    }
  };

  return (
    <View style={styles.container}>
      {/* Mode Toggle */}
      <TouchableOpacity
        style={styles.modeToggle}
        onPress={onModeToggle}
        disabled={disabled || status === 'running'}
      >
        <Text style={styles.modeToggleText}>
          {mode === 'pomodoro' ? '🍅 Pomodoro' : '⏱️ Stopwatch'}
        </Text>
      </TouchableOpacity>

      {/* Primary Control Button */}
      <View style={styles.primaryButtonContainer}>
        <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
          <TouchableOpacity
            style={[
              styles.primaryButton,
              status === 'running' && styles.primaryButtonRunning,
              disabled && styles.primaryButtonDisabled,
            ]}
            onPress={handlePrimaryPress}
            disabled={disabled}
            activeOpacity={0.8}
          >
            <Text style={styles.primaryButtonIcon}>
              {getPrimaryButtonIcon()}
            </Text>
            <Text
              style={[
                styles.primaryButtonText,
                disabled && styles.primaryButtonTextDisabled,
              ]}
            >
              {getPrimaryButtonText()}
            </Text>
          </TouchableOpacity>
        </Animated.View>
      </View>

      {/* Secondary Controls */}
      <View style={styles.secondaryControls}>
        {/* Reset Button */}
        <TouchableOpacity
          style={[
            styles.secondaryButton,
            disabled && styles.secondaryButtonDisabled,
          ]}
          onPress={onReset}
          disabled={disabled || status === 'idle'}
        >
          <Text style={styles.secondaryButtonIcon}>🔄</Text>
          <Text
            style={[
              styles.secondaryButtonText,
              disabled && styles.secondaryButtonTextDisabled,
            ]}
          >
            RESET
          </Text>
        </TouchableOpacity>

        {/* Settings Button - Placeholder for future implementation */}
        <TouchableOpacity
          style={[
            styles.secondaryButton,
            disabled && styles.secondaryButtonDisabled,
          ]}
          disabled={disabled}
        >
          <Text style={styles.secondaryButtonIcon}>⚙️</Text>
          <Text
            style={[
              styles.secondaryButtonText,
              disabled && styles.secondaryButtonTextDisabled,
            ]}
          >
            SETTINGS
          </Text>
        </TouchableOpacity>
      </View>

      {/* Status Indicator */}
      <View style={styles.statusIndicator}>
        <View style={[styles.statusDot, getStatusDotStyle()]} />
        <Text style={styles.statusText}>{getStatusText()}</Text>
      </View>
    </View>
  );
};

export default TimerControls;
