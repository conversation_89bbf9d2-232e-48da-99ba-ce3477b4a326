import React, { useState, useEffect, useRef, useCallback } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { Audio } from 'expo-av';
import * as Notifications from 'expo-notifications';

// ===========================================
// 🍅 TIMER CORE LOGIC
// ===========================================

export type TimerMode = 'pomodoro' | 'stopwatch';
export type TimerStatus = 'idle' | 'running' | 'paused';
export type PomodoroPhase = 'work' | 'shortBreak' | 'longBreak';

export interface TimerSettings {
  workDuration: number; // seconds
  shortBreakDuration: number; // seconds
  longBreakDuration: number; // seconds
  sessionsUntilLongBreak: number;
  autoStartBreaks: boolean;
  autoStartWork: boolean;
  soundEnabled: boolean;
  notificationsEnabled: boolean;
  vibrationEnabled: boolean;
}

export interface TimerState {
  mode: TimerMode;
  status: TimerStatus;
  displayTime: number; // seconds
  currentPhase: PomodoroPhase;
  completedSessions: number;
  settings: TimerSettings;
}

export interface TimerCallbacks {
  onTick?: (time: number) => void;
  onPhaseComplete?: (phase: PomodoroPhase, nextPhase: PomodoroPhase) => void;
  onSessionComplete?: (completedSessions: number) => void;
  onTimerStart?: () => void;
  onTimerPause?: () => void;
  onTimerReset?: () => void;
}

// Default settings
export const defaultTimerSettings: TimerSettings = {
  workDuration: 25 * 60, // 25 minutes
  shortBreakDuration: 5 * 60, // 5 minutes
  longBreakDuration: 15 * 60, // 15 minutes
  sessionsUntilLongBreak: 4,
  autoStartBreaks: false,
  autoStartWork: false,
  soundEnabled: true,
  notificationsEnabled: true,
  vibrationEnabled: true,
};

// ===========================================
// 🎵 AUDIO MANAGER
// ===========================================

class AudioManager {
  private static instance: AudioManager;
  private sounds: { [key: string]: Audio.Sound } = {};
  private isLoaded = false;

  static getInstance(): AudioManager {
    if (!AudioManager.instance) {
      AudioManager.instance = new AudioManager();
    }
    return AudioManager.instance;
  }

  async initialize() {
    if (this.isLoaded) return;

    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: false,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });

      // Load notification sounds (with fallback for missing files)
      try {
        const { sound: completionSound } = await Audio.Sound.createAsync(
          { uri: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav' },
          { shouldPlay: false }
        );
        this.sounds.completion = completionSound;
      } catch (error) {
        console.warn('Failed to load completion sound, using system sound');
      }

      try {
        const { sound: tickSound } = await Audio.Sound.createAsync(
          { uri: 'https://www.soundjay.com/misc/sounds/click-03.wav' },
          { shouldPlay: false }
        );
        this.sounds.tick = tickSound;
      } catch (error) {
        console.warn('Failed to load tick sound, using system sound');
      }

      this.isLoaded = true;
    } catch (error) {
      console.warn('Failed to initialize audio:', error);
    }
  }

  async playSound(soundName: string, volume: number = 1.0) {
    try {
      const sound = this.sounds[soundName];
      if (sound) {
        await sound.setVolumeAsync(volume);
        await sound.replayAsync();
      }
    } catch (error) {
      console.warn(`Failed to play sound ${soundName}:`, error);
    }
  }

  async cleanup() {
    for (const sound of Object.values(this.sounds)) {
      try {
        await sound.unloadAsync();
      } catch (error) {
        console.warn('Failed to unload sound:', error);
      }
    }
    this.sounds = {};
    this.isLoaded = false;
  }
}

// ===========================================
// 🔔 NOTIFICATION MANAGER
// ===========================================

class NotificationManager {
  private static instance: NotificationManager;
  private hasPermission = false;

  static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  async initialize() {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      this.hasPermission = finalStatus === 'granted';

      // Configure notification behavior
      Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: false,
        }),
      });
    } catch (error) {
      console.warn('Failed to initialize notifications:', error);
    }
  }

  async scheduleNotification(title: string, body: string, trigger?: Notifications.NotificationTriggerInput) {
    if (!this.hasPermission) return;

    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          sound: 'default',
        },
        trigger: trigger || null,
      });
    } catch (error) {
      console.warn('Failed to schedule notification:', error);
    }
  }

  async cancelAllNotifications() {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.warn('Failed to cancel notifications:', error);
    }
  }
}

// ===========================================
// ⏱️ TIMER CORE HOOK
// ===========================================

export const useTimerCore = (
  initialSettings: Partial<TimerSettings> = {},
  callbacks: TimerCallbacks = {}
) => {
  const [state, setState] = useState<TimerState>({
    mode: 'pomodoro',
    status: 'idle',
    displayTime: 0,
    currentPhase: 'work',
    completedSessions: 0,
    settings: { ...defaultTimerSettings, ...initialSettings },
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number | null>(null);
  const pausedTimeRef = useRef<number>(0);
  const backgroundTimeRef = useRef<number | null>(null);
  const audioManager = useRef(AudioManager.getInstance());
  const notificationManager = useRef(NotificationManager.getInstance());

  // Initialize audio and notifications
  useEffect(() => {
    audioManager.current.initialize();
    notificationManager.current.initialize();

    return () => {
      audioManager.current.cleanup();
    };
  }, []);

  // Handle app state changes for background timer
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (state.status !== 'running') return;

      if (nextAppState === 'background') {
        backgroundTimeRef.current = Date.now();
      } else if (nextAppState === 'active' && backgroundTimeRef.current) {
        const backgroundDuration = Date.now() - backgroundTimeRef.current;
        pausedTimeRef.current += backgroundDuration;
        backgroundTimeRef.current = null;
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [state.status]);

  // Timer tick logic
  const tick = useCallback(() => {
    if (!startTimeRef.current) return;

    const now = Date.now();
    const elapsed = Math.floor((now - startTimeRef.current - pausedTimeRef.current) / 1000);

    if (state.mode === 'pomodoro') {
      const phaseDuration = getPhaseDuration(state.currentPhase, state.settings);
      const remaining = Math.max(0, phaseDuration - elapsed);
      
      setState(prev => ({ ...prev, displayTime: remaining }));
      callbacks.onTick?.(remaining);

      if (remaining <= 0) {
        handlePhaseComplete();
      }
    } else {
      // Stopwatch mode
      setState(prev => ({ ...prev, displayTime: elapsed }));
      callbacks.onTick?.(elapsed);
    }
  }, [state.mode, state.currentPhase, state.settings, callbacks]);

  // Start timer interval
  useEffect(() => {
    if (state.status === 'running') {
      intervalRef.current = setInterval(tick, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [state.status, tick]);

  // Get phase duration helper
  const getPhaseDuration = (phase: PomodoroPhase, settings: TimerSettings): number => {
    switch (phase) {
      case 'work':
        return settings.workDuration;
      case 'shortBreak':
        return settings.shortBreakDuration;
      case 'longBreak':
        return settings.longBreakDuration;
      default:
        return settings.workDuration;
    }
  };

  // Handle phase completion
  const handlePhaseComplete = useCallback(async () => {
    const { currentPhase, completedSessions, settings } = state;
    
    // Play completion sound
    if (settings.soundEnabled) {
      await audioManager.current.playSound('completion');
    }

    // Determine next phase
    let nextPhase: PomodoroPhase;
    let newCompletedSessions = completedSessions;

    if (currentPhase === 'work') {
      newCompletedSessions += 1;
      nextPhase = newCompletedSessions % settings.sessionsUntilLongBreak === 0 
        ? 'longBreak' 
        : 'shortBreak';
    } else {
      nextPhase = 'work';
    }

    // Send notification
    if (settings.notificationsEnabled) {
      const phaseNames = {
        work: 'Work Session',
        shortBreak: 'Short Break',
        longBreak: 'Long Break',
      };
      
      await notificationManager.current.scheduleNotification(
        `${phaseNames[currentPhase]} Complete!`,
        `Time to start your ${phaseNames[nextPhase].toLowerCase()}.`
      );
    }

    // Update state
    setState(prev => ({
      ...prev,
      currentPhase: nextPhase,
      completedSessions: newCompletedSessions,
      displayTime: getPhaseDuration(nextPhase, settings),
      status: settings.autoStartBreaks || (nextPhase === 'work' && settings.autoStartWork) 
        ? 'running' 
        : 'paused',
    }));

    // Reset timer references for new phase
    startTimeRef.current = Date.now();
    pausedTimeRef.current = 0;

    // Trigger callbacks
    callbacks.onPhaseComplete?.(currentPhase, nextPhase);
    if (currentPhase === 'work') {
      callbacks.onSessionComplete?.(newCompletedSessions);
    }
  }, [state, callbacks]);

  return {
    state,
    setState,
    audioManager: audioManager.current,
    notificationManager: notificationManager.current,
    startTimeRef,
    pausedTimeRef,
    getPhaseDuration,
  };
};
