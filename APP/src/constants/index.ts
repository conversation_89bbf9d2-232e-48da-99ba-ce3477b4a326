// Constants exports
// This file will be populated as constants are created

// Re-export all expressive theme constants including typography
export * from './expressiveTheme';

// Typography-specific exports for easier access
export {
  ExpressiveTypography,
  ExpressiveTypographyVariants,
  EmotionalTypography,
  ExpressiveFontWeights,
  ExpressiveFontFamilies,
  getTypographyStyle,
  getTypographyVariant,
  getEmotionalTypography,
  getFontWeight,
  getFontFamily,
} from './expressiveTheme';

// Dynamic color system exports
export * from './dynamicColors';
export {
  FallbackSeedColors,
  defaultDynamicColorConfig,
  getMaterialYouPalette,
  generatePaletteFromSeedColor,
  mapMaterialYouToTheme,
  isMaterialYouSupported,
  getContextualGenerationStyle,
  MaterialYou,
} from './dynamicColors';

// export * from './expressiveMotion';

// Placeholder to prevent TypeScript errors
export {};
