import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { Appearance, ColorSchemeName } from 'react-native';
import { 
  getMaterialYouPalette, 
  mapMaterialYouToTheme, 
  generatePaletteFromSeedColor,
  isMaterialYouSupported,
  defaultDynamicColorConfig,
  FallbackSeedColors,
  type DynamicColorConfig,
  type GenerationStyle,
  type MaterialYouPalette 
} from '../constants/dynamicColors';
import { 
  expressiveLightTheme, 
  expressiveDarkTheme, 
  ExpressiveColors 
} from '../constants/expressiveTheme';

// ===========================================
// 🎨 DYNAMIC THEME CONTEXT
// ===========================================

interface DynamicTheme {
  // Theme data
  colors: typeof expressiveLightTheme.colors;
  fonts: typeof expressiveLightTheme.fonts;
  expressive: typeof expressiveLightTheme.expressive;
  isDark: boolean;
  
  // Material You data
  materialYouPalette: MaterialYouPalette;
  isMaterialYouEnabled: boolean;
  isMaterialYouSupported: boolean;
  
  // Configuration
  config: DynamicColorConfig;
  
  // Theme controls
  setColorScheme: (scheme: 'light' | 'dark' | 'auto') => void;
  setSeedColor: (color: string) => void;
  setGenerationStyle: (style: GenerationStyle) => void;
  setDynamicColorsEnabled: (enabled: boolean) => void;
  
  // Utilities
  generateThemeFromSeed: (seedColor: string, style?: GenerationStyle) => void;
  resetToDefault: () => void;
  getCurrentSeedColor: () => string;
}

const DynamicThemeContext = createContext<DynamicTheme | null>(null);

// ===========================================
// 🎨 DYNAMIC THEME PROVIDER
// ===========================================

interface DynamicThemeProviderProps {
  children: React.ReactNode;
  initialConfig?: Partial<DynamicColorConfig>;
  initialColorScheme?: 'light' | 'dark' | 'auto';
}

export const DynamicThemeProvider: React.FC<DynamicThemeProviderProps> = ({
  children,
  initialConfig = {},
  initialColorScheme = 'auto',
}) => {
  // Configuration state
  const [config, setConfig] = useState<DynamicColorConfig>({
    ...defaultDynamicColorConfig,
    ...initialConfig,
  });
  
  // Theme state
  const [colorScheme, setColorSchemeState] = useState<'light' | 'dark' | 'auto'>(initialColorScheme);
  const [systemColorScheme, setSystemColorScheme] = useState<ColorSchemeName>(
    Appearance.getColorScheme()
  );
  const [materialYouPalette, setMaterialYouPalette] = useState<MaterialYouPalette | null>(null);
  const [currentSeedColor, setCurrentSeedColor] = useState<string>(config.fallbackSeedColor);
  
  // Computed values
  const isDark = colorScheme === 'auto' 
    ? systemColorScheme === 'dark' 
    : colorScheme === 'dark';
  
  const isMaterialYouSupportedValue = isMaterialYouSupported();
  
  // Initialize Material You palette
  useEffect(() => {
    const initializePalette = async () => {
      try {
        const palette = getMaterialYouPalette(config);
        setMaterialYouPalette(palette);
      } catch (error) {
        console.warn('Failed to initialize Material You palette:', error);
        // Generate fallback palette
        const fallbackPalette = generatePaletteFromSeedColor(
          config.fallbackSeedColor,
          config.generationStyle
        );
        setMaterialYouPalette(fallbackPalette);
      }
    };
    
    initializePalette();
  }, [config]);
  
  // Listen to system color scheme changes
  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme: newColorScheme }) => {
      setSystemColorScheme(newColorScheme);
    });
    
    return () => subscription?.remove();
  }, []);
  
  // Generate theme colors from Material You palette or fallback to expressive colors
  const generateThemeColors = useCallback(() => {
    if (config.enabled && materialYouPalette) {
      // Use Material You colors
      const dynamicColors = mapMaterialYouToTheme(materialYouPalette, isDark);
      
      // Merge with expressive theme structure
      const baseTheme = isDark ? expressiveDarkTheme : expressiveLightTheme;
      
      return {
        ...baseTheme.colors,
        ...dynamicColors,
        // Keep custom expressive colors for specific use cases
        success: baseTheme.colors.success,
        onSuccess: baseTheme.colors.onSuccess,
        successContainer: baseTheme.colors.successContainer,
        onSuccessContainer: baseTheme.colors.onSuccessContainer,
        warning: baseTheme.colors.warning,
        onWarning: baseTheme.colors.onWarning,
        warningContainer: baseTheme.colors.warningContainer,
        onWarningContainer: baseTheme.colors.onWarningContainer,
      };
    } else {
      // Use static expressive colors
      const baseTheme = isDark ? expressiveDarkTheme : expressiveLightTheme;
      return baseTheme.colors;
    }
  }, [config.enabled, materialYouPalette, isDark]);
  
  // Theme controls
  const setColorScheme = useCallback((scheme: 'light' | 'dark' | 'auto') => {
    setColorSchemeState(scheme);
  }, []);
  
  const setSeedColor = useCallback((color: string) => {
    setCurrentSeedColor(color);
    const newPalette = generatePaletteFromSeedColor(color, config.generationStyle);
    setMaterialYouPalette(newPalette);
  }, [config.generationStyle]);
  
  const setGenerationStyle = useCallback((style: GenerationStyle) => {
    const newConfig = { ...config, generationStyle: style };
    setConfig(newConfig);
    
    // Regenerate palette with new style
    const newPalette = generatePaletteFromSeedColor(currentSeedColor, style);
    setMaterialYouPalette(newPalette);
  }, [config, currentSeedColor]);
  
  const setDynamicColorsEnabled = useCallback((enabled: boolean) => {
    const newConfig = { ...config, enabled };
    setConfig(newConfig);
  }, [config]);
  
  const generateThemeFromSeed = useCallback((seedColor: string, style?: GenerationStyle) => {
    const generationStyle = style || config.generationStyle;
    setCurrentSeedColor(seedColor);
    
    const newPalette = generatePaletteFromSeedColor(seedColor, generationStyle);
    setMaterialYouPalette(newPalette);
    
    if (style) {
      setConfig(prev => ({ ...prev, generationStyle: style }));
    }
  }, [config.generationStyle]);
  
  const resetToDefault = useCallback(() => {
    setConfig(defaultDynamicColorConfig);
    setCurrentSeedColor(defaultDynamicColorConfig.fallbackSeedColor);
    setColorSchemeState('auto');
    
    // Regenerate default palette
    const defaultPalette = getMaterialYouPalette(defaultDynamicColorConfig);
    setMaterialYouPalette(defaultPalette);
  }, []);
  
  const getCurrentSeedColor = useCallback(() => {
    return currentSeedColor;
  }, [currentSeedColor]);
  
  // Build final theme
  const theme: DynamicTheme = {
    // Theme data
    colors: generateThemeColors(),
    fonts: (isDark ? expressiveDarkTheme : expressiveLightTheme).fonts,
    expressive: (isDark ? expressiveDarkTheme : expressiveLightTheme).expressive,
    isDark,
    
    // Material You data
    materialYouPalette: materialYouPalette || generatePaletteFromSeedColor(FallbackSeedColors.primary),
    isMaterialYouEnabled: config.enabled,
    isMaterialYouSupported: isMaterialYouSupportedValue,
    
    // Configuration
    config,
    
    // Theme controls
    setColorScheme,
    setSeedColor,
    setGenerationStyle,
    setDynamicColorsEnabled,
    
    // Utilities
    generateThemeFromSeed,
    resetToDefault,
    getCurrentSeedColor,
  };
  
  return (
    <DynamicThemeContext.Provider value={theme}>
      {children}
    </DynamicThemeContext.Provider>
  );
};

// ===========================================
// 🎨 THEME HOOK
// ===========================================

export const useDynamicTheme = (): DynamicTheme => {
  const context = useContext(DynamicThemeContext);
  if (!context) {
    throw new Error('useDynamicTheme must be used within a DynamicThemeProvider');
  }
  return context;
};

// ===========================================
// 🎨 THEME UTILITIES
// ===========================================

// HOC for components that need theme
export function withDynamicTheme<P extends object>(
  Component: React.ComponentType<P & { theme: DynamicTheme }>
) {
  return function ThemedComponent(props: P) {
    const theme = useDynamicTheme();
    return <Component {...props} theme={theme} />;
  };
}

// Hook for accessing only colors (most common use case)
export const useDynamicColors = () => {
  const { colors } = useDynamicTheme();
  return colors;
};

// Hook for Material You specific features
export const useMaterialYou = () => {
  const { 
    materialYouPalette, 
    isMaterialYouEnabled, 
    isMaterialYouSupported,
    setSeedColor,
    setGenerationStyle,
    generateThemeFromSeed,
    getCurrentSeedColor
  } = useDynamicTheme();
  
  return {
    palette: materialYouPalette,
    isEnabled: isMaterialYouEnabled,
    isSupported: isMaterialYouSupported,
    setSeedColor,
    setGenerationStyle,
    generateFromSeed: generateThemeFromSeed,
    getCurrentSeedColor,
  };
};
