import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useDynamicTheme } from '../contexts/DynamicThemeProvider';
import Timer from '../components/timer/Timer';

// ===========================================
// ⏱️ TIMER SCREEN
// ===========================================

export const TimerScreen: React.FC = () => {
  const theme = useDynamicTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
  });

  return (
    <View style={styles.container}>
      <Timer 
        initialMode="pomodoro"
        onSessionComplete={(sessions) => console.log(`Completed ${sessions} sessions`)}
        onPhaseChange={(phase) => console.log(`Phase changed to ${phase}`)}
      />
    </View>
  );
};

export default TimerScreen;
